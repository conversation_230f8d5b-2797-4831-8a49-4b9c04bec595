{"name": "server", "version": "1.0.0", "main": "index.js", "type": "commonjs", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@vonage/server-sdk": "^3.19.2", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "firebase-admin": "^12.4.0", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "natural": "^8.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "openai": "^4.77.0", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3", "slugify": "^1.6.6", "socket.io": "^4.8.1", "uuid": "^10.0.0", "validator": "^13.12.0"}, "devDependencies": {"nodemon": "^3.1.4", "sequelize-cli": "^6.6.2"}}