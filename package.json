{"name": "mobile", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "proxy": "http://localhost:3000", "dependencies": {"@expo/config": "~9.0.0", "@expo/metro-config": "~0.18.11", "@expo/metro-runtime": "~3.2.3", "@expo/vector-icons": "^14.0.4", "@gorhom/bottom-sheet": "^5.0.4", "@howljs/calendar-kit": "^2.2.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-firebase/app": "^19.0.0", "@react-native-firebase/auth": "^19.0.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.2.8", "@shopify/react-native-skia": "1.2.3", "axios": "^1.7.7", "babel-plugin-module-resolver": "^5.0.2", "dayjs": "^1.11.13", "expo": "~51.0.39", "expo-barcode-generator": "^3.0.2", "expo-barcode-scanner": "~13.0.1", "expo-camera": "~15.0.16", "expo-constants": "~16.0.2", "expo-dev-client": "~4.0.29", "expo-haptics": "~13.0.1", "expo-image-picker": "~15.0.7", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-notifications": "~0.28.19", "expo-splash-screen": "~0.27.7", "expo-status-bar": "~1.12.1", "firebase": "^10.14.1", "jsbarcode": "^3.11.6", "lodash": "^4.17.21", "lottie-react-native": "6.7.0", "moment": "^2.30.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-barcode-builder": "^2.0.0", "react-native-big-calendar": "^4.15.2", "react-native-calendars": "^1.1307.0", "react-native-element-dropdown": "^2.12.2", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-paper": "^5.12.5", "react-native-reanimated": "~3.10.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "15.2.0", "react-native-svg-transformer": "^1.5.0", "react-native-swiper-flatlist": "^3.2.5", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.10", "react-native-webview": "^13.8.6", "react-redux": "^9.1.2", "socket.io-client": "^4.8.1", "typescript": "~5.3.3"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}