{"name": "hospital", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^43.1.0", "@ckeditor/ckeditor5-react": "^9.1.0", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@reduxjs/toolkit": "^2.2.7", "@tinymce/tinymce-react": "^5.1.1", "antd": "^5.21.3", "axios": "^1.7.7", "chart.js": "^4.4.7", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "html-to-text": "^9.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.14.1", "react-calendar": "^5.0.0", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-qr-barcode-scanner": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.25.1", "redux-persist": "^6.0.0", "styled-components": "^6.1.13", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "vite": "^5.3.4"}}