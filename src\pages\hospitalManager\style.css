/* Đ<PERSON><PERSON> với thanh cuộn trên mọi trình du<PERSON>t */
.scroll-container {
  height: 100vh; /* Hoặc chiều cao bạn muốn */
  overflow-y: auto; /* <PERSON> phép cuộn dọc */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #d6d4d4 #f1f1f1; /* Màu thanh cuộn và nền */
}
.ant-select-outlined {
  border-radius: none !important;
}
/* Đ<PERSON>i với thanh cuộn trên Webkit (Chrome, Safari) */
.scroll-container::-webkit-scrollbar {
  width: 8px; /* Đ<PERSON> rộng của thanh cuộn */
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1; /* Màu nền của thanh cuộn */
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #888; /* Màu của thanh cuộn */
  border-radius: 4px; /* Bo góc thanh cuộn */
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555; /* <PERSON><PERSON><PERSON> của thanh cuộn khi hover */
}

.ant-table-thead > tr > th.ant-table-cell {
  background-color: #dbeafe !important;
  color: #000 !important;
  font-weight: 500 !important;
  border: none !important;
}

.ant-table-tbody > tr:hover {
  cursor: pointer !important;
}

/* .rbc-event {
  background-color: #0165ff !important;
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
  border: none !important;
} */

/* .event-selected {
  background-color: #cce0fe !important;
  border-left: 6px solid #0165ff !important;
} */
/* .rbc-event-label {
  display: none !important;
} */
.ck-editor__editable_inline {
  max-height: 200px;
  min-height: 200px;
}
