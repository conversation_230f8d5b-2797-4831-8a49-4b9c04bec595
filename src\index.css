@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
* {
  margin: 0;
  padding: 10;
  border: none;
  outline: none;
  box-sizing: border-box;
  font-family: "Roboto", sans-serif;
}

.app-name {
  display: flex;
  flex-direction: row;
  gap: 10px;
  cursor: pointer;
  align-items: center;
}

.sidebar {
  color: #fff;
}

.menu-bar {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 5px;
  font-size: 24;
}

.toggle-theme-btn {
  position: absolute;
  bottom: 0px;
  left: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.no-padding {
  padding: 0 !important;
}

.ant-table-thead .ant-table-cell {
  background-color: #fff !important;
  border-radius: 0px !important;
  border-bottom: 1px solid #636363 !important;
}
